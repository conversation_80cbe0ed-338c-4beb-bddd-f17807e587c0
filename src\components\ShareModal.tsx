'use client';

import { useState } from 'react';
import { Boss } from '@/types/boss';
import { useSharing } from '@/hooks/useSharing';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  customBosses: Boss[];
  timerStates: Record<string, any>;
}

export default function ShareModal({ isOpen, onClose, customBosses, timerStates }: ShareModalProps) {
  const [password, setPassword] = useState('');
  const [sharedBy, setSharedBy] = useState('');
  const [usePassword, setUsePassword] = useState(false);
  const [copied, setCopied] = useState(false);
  
  const { 
    isSharing, 
    shareUrl, 
    error, 
    createShareLink, 
    copyToClipboard, 
    clearError, 
    clearShareUrl 
  } = useSharing();

  const handleCreateShare = async () => {
    clearError();
    try {
      const sharePassword = usePassword ? password : undefined;
      const url = await createShareLink(customBosses, timerStates, sharePassword, sharedBy || undefined);
      console.log('Share URL created:', url);
    } catch (error) {
      console.error('Failed to create share link:', error);
    }
  };

  const handleCopyLink = async () => {
    if (shareUrl) {
      const success = await copyToClipboard(shareUrl);
      if (success) {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      }
    }
  };

  const handleClose = () => {
    clearShareUrl();
    clearError();
    setPassword('');
    setSharedBy('');
    setUsePassword(false);
    setCopied(false);
    onClose();
  };

  const getShareStats = () => {
    const activeBosses = Object.values(timerStates).filter(state => state.isActive).length;
    const totalBosses = customBosses.length + Object.keys(timerStates).length;
    return { activeBosses, totalBosses, customBosses: customBosses.length };
  };

  if (!isOpen) return null;

  const stats = getShareStats();

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      {/* Background overlay */}
      <div 
        className="absolute inset-0"
        onClick={handleClose}
      />

      {/* Modal panel */}
      <div className="relative w-full max-w-2xl bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
            Share Boss Timers
          </h3>
          <button
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Share Stats */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            What will be shared:
          </h4>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {stats.totalBosses}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Total Bosses</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {stats.activeBosses}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Active Timers</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {stats.customBosses}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Custom Bosses</div>
            </div>
          </div>
        </div>

        {!shareUrl ? (
          /* Share Configuration */
          <div className="space-y-4">
            {/* Shared By */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Your Name (Optional)
              </label>
              <input
                type="text"
                value={sharedBy}
                onChange={(e) => setSharedBy(e.target.value)}
                placeholder="Enter your name or guild"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            {/* Password Protection */}
            <div>
              <div className="flex items-center mb-2">
                <input
                  type="checkbox"
                  id="usePassword"
                  checked={usePassword}
                  onChange={(e) => setUsePassword(e.target.checked)}
                  className="mr-2"
                />
                <label htmlFor="usePassword" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Password protect this share
                </label>
              </div>
              
              {usePassword && (
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              )}
            </div>

            {/* Error Display */}
            {error && (
              <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded">
                {error}
              </div>
            )}

            {/* Create Share Button */}
            <button
              onClick={handleCreateShare}
              disabled={isSharing || (usePassword && !password.trim())}
              className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors flex items-center justify-center gap-2"
            >
              {isSharing ? (
                <>
                  <svg className="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Creating Share Link...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                  </svg>
                  Create Share Link
                </>
              )}
            </button>
          </div>
        ) : (
          /* Share Link Display */
          <div className="space-y-4">
            <div className="bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded">
              <div className="flex items-center gap-2 mb-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="font-medium">Share link created successfully!</span>
              </div>
              <p className="text-sm">
                Share this link with others to let them import your boss timers and custom bosses.
                {usePassword && ' They will need the password you set.'}
              </p>
            </div>

            {/* Share Link */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Share Link
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={shareUrl}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                />
                <button
                  onClick={handleCopyLink}
                  className={`px-4 py-2 rounded-md transition-colors text-sm ${
                    copied 
                      ? 'bg-green-600 text-white' 
                      : 'bg-gray-600 hover:bg-gray-700 text-white'
                  }`}
                >
                  {copied ? 'Copied!' : 'Copy'}
                </button>
              </div>
            </div>

            {/* Password Display */}
            {usePassword && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Password (share this separately)
                </label>
                <div className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-mono">
                  {password}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-2">
              <button
                onClick={() => {
                  clearShareUrl();
                  setPassword('');
                  setUsePassword(false);
                }}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Create New Link
              </button>
              <button
                onClick={handleClose}
                className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
              >
                Done
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
