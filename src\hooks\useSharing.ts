import { useState, useCallback } from 'react';
import { Boss } from '@/types/boss';

interface TimerState {
  isActive: boolean;
  lastKilled?: string; // ISO string
  nextRespawn?: string; // ISO string
}

interface TimerData {
  customBosses: Boss[];
  timerStates: Record<string, TimerState>;
  sharedAt: string;
  sharedBy?: string;
}

interface ShareData {
  data: TimerData;
  password?: string;
  hasPassword: boolean;
}

// Simple encryption/decryption using base64 and password
const encryptData = (data: string, password: string): string => {
  const encoded = btoa(data);
  const passwordHash = btoa(password);
  return btoa(encoded + '|' + passwordHash);
};

const decryptData = (encryptedData: string, password: string): string | null => {
  try {
    const decoded = atob(encryptedData);
    const [data, passwordHash] = decoded.split('|');
    const expectedPasswordHash = btoa(password);
    
    if (passwordHash !== expectedPasswordHash) {
      return null; // Wrong password
    }
    
    return atob(data);
  } catch (error) {
    return null;
  }
};

export function useSharing() {
  const [isSharing, setIsSharing] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [shareUrl, setShareUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const generateShareData = useCallback((
    customBosses: Boss[],
    timerStates: Record<string, TimerState & { lastKilled?: Date; nextRespawn?: Date }>,
    sharedBy?: string
  ): TimerData => {
    // Convert Date objects to ISO strings for serialization
    const serializedTimerStates: Record<string, TimerState> = {};
    
    Object.entries(timerStates).forEach(([bossId, state]) => {
      serializedTimerStates[bossId] = {
        isActive: state.isActive,
        lastKilled: state.lastKilled?.toISOString(),
        nextRespawn: state.nextRespawn?.toISOString(),
      };
    });

    return {
      customBosses,
      timerStates: serializedTimerStates,
      sharedAt: new Date().toISOString(),
      sharedBy,
    };
  }, []);

  const createShareLink = useCallback(async (
    customBosses: Boss[],
    timerStates: Record<string, TimerState & { lastKilled?: Date; nextRespawn?: Date }>,
    password?: string,
    sharedBy?: string
  ): Promise<string> => {
    setIsSharing(true);
    setError(null);

    try {
      const timerData = generateShareData(customBosses, timerStates, sharedBy);
      const shareData: ShareData = {
        data: timerData,
        hasPassword: !!password,
      };

      let dataToShare = JSON.stringify(shareData);
      
      if (password) {
        dataToShare = encryptData(dataToShare, password);
      }

      // Encode the data for URL
      const encodedData = btoa(dataToShare);
      
      // Create share URL (in a real app, you might use a URL shortener service)
      const baseUrl = window.location.origin + window.location.pathname;
      const shareUrl = `${baseUrl}?share=${encodedData}`;
      
      setShareUrl(shareUrl);
      return shareUrl;
    } catch {
      const errorMessage = 'Failed to create share link';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsSharing(false);
    }
  }, [generateShareData]);

  const parseShareData = useCallback((shareCode: string, password?: string): TimerData | null => {
    setIsImporting(true);
    setError(null);

    try {
      const decodedData = atob(shareCode);
      
      // Try to parse as JSON first (non-encrypted)
      let shareData: ShareData;
      try {
        shareData = JSON.parse(decodedData);
      } catch {
        // If parsing fails, it might be encrypted
        if (!password) {
          setError('This shared data requires a password');
          return null;
        }
        
        const decryptedData = decryptData(decodedData, password);
        if (!decryptedData) {
          setError('Invalid password');
          return null;
        }
        
        shareData = JSON.parse(decryptedData);
      }

      // If data has password but none provided
      if (shareData.hasPassword && !password) {
        setError('This shared data requires a password');
        return null;
      }

      // Convert ISO strings back to Date objects
      const timerStates: Record<string, TimerState & { lastKilled?: Date; nextRespawn?: Date }> = {};
      Object.entries(shareData.data.timerStates).forEach(([bossId, state]) => {
        timerStates[bossId] = {
          isActive: state.isActive,
          lastKilled: state.lastKilled ? new Date(state.lastKilled) : undefined,
          nextRespawn: state.nextRespawn ? new Date(state.nextRespawn) : undefined,
        };
      });

      return {
        ...shareData.data,
        timerStates,
      };
    } catch {
      setError('Invalid share code or corrupted data');
      return null;
    } finally {
      setIsImporting(false);
    }
  }, []);

  const copyToClipboard = useCallback(async (text: string): Promise<boolean> => {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        document.body.removeChild(textArea);
        return true;
      } catch (fallbackError) {
        document.body.removeChild(textArea);
        return false;
      }
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearShareUrl = useCallback(() => {
    setShareUrl(null);
  }, []);

  return {
    isSharing,
    isImporting,
    shareUrl,
    error,
    createShareLink,
    parseShareData,
    copyToClipboard,
    clearError,
    clearShareUrl,
  };
}
